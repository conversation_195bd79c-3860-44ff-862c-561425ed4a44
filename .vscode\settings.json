{"editor.minimap.enabled": true, "workbench.colorTheme": "Default Dark+", "git.ignoreMissingGitWarning": true, "workbench.iconTheme": "vscode-icons", "sublimeTextKeymap.promptV3Features": true, "editor.multiCursorModifier": "ctrlCmd", "editor.snippetSuggestions": "top", "editor.formatOnPaste": true, "explorer.confirmDelete": false, "todohighlight.isEnable": true, "editor.formatOnSave": true, "typescript.format.placeOpenBraceOnNewLineForFunctions": true, "typescript.format.placeOpenBraceOnNewLineForControlBlocks": true, "typescript.format.insertSpaceAfterOpeningAndBeforeClosingNonemptyParenthesis": true, "typescript.format.insertSpaceAfterOpeningAndBeforeClosingNonemptyBrackets": true, "typescript.suggest.enabled": true, "typescript.preferences.quoteStyle": "double"}